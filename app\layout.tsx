import type { Metada<PERSON> } from "next";
import { Inter, <PERSON>o_Mono } from "next/font/google";
import "./globals.css";
import IndexLayout from "@/components/indexLayout/indexLayout";
import { SidebarContextProvider } from "@/src/context/sidebar.context";

const geistSans = Inter({
    variable: "--font-geist-sans",
    subsets: ["latin"],
});

const geistMono = Roboto_Mono({
    variable: "--font-geist-mono",
    subsets: ["latin"],
});

export const metadata: Metadata = {
    title: "QleanOn | Admin Panel",
    description:
        "QleanOn Admin simplifies bookings, cleaner assignments, schedules, and more — all in one platform.",
};

export default function RootLayout({
    children,
}: Readonly<{
    children: React.ReactNode;
}>) {
    return (
        <html lang="en">
            <body
                className={`${geistSans.variable} ${geistMono.variable} antialiased`}
            >
                <SidebarContextProvider>
                    <IndexLayout>{children}</IndexLayout>
                </SidebarContextProvider>
            </body>
        </html>
    );
}
