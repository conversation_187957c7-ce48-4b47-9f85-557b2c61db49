"use client";
import React, { useState } from "react";
import { Dropdown, Empty, MenuProps, Pagination, Switch, Table } from "antd";
import { Montserrat } from "next/font/google";
import { ColumnsType } from "antd/es/table";
import Image from "next/image";
import { IoEyeOutline } from "react-icons/io5";
import { RxCheck, RxCross2, Rx<PERSON>rossCircled } from "react-icons/rx";
import { RxCheckCircled } from "react-icons/rx";
import { AiOutlineEye } from "react-icons/ai";
import ActionModal from "./action-modal";

const montserrat = Montserrat({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const ManageCleanerTable = ({
    data,
    fetch,
    pagination,
    fetchAllCleanersDataList,
    alignValue,
    search,
}: any) => {
    const [rowCleanerRecord, setRowCleanerRecord] = useState("");
    const [modalOpen, setModalOpen] = useState(false);
    const [actionType, setActionType] = useState<"APPROVE" | "REJECT">("REJECT");

    const cleanerManagementColumns: ColumnsType<any> = [
        {
            title: <div className={montserrat.className}>Image</div>,
            dataIndex: "profile",
            key: "profile",
            align: "center",
            className: montserrat.className,
            width: 100,
            render: (profile: string, record: any) => {
                return (
                    <div className="flex justify-center items-center">
                        {profile ? (
                            <img
                                height={48}
                                width={48}
                                alt="Profile Picture"
                                src={profile}
                                className="rounded-full object-cover"
                            />
                        ) : record.name ? (
                            <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-[18px] font-semibold">
                                {record.name.charAt(0).toUpperCase()}
                            </div>
                        ) : (
                            <img
                                src="/images/profile-circle.svg"
                                alt="Profile"
                                width={48}
                                height={48}
                            />
                        )}
                    </div>
                );
            },
        },
        {
            title: <div className={montserrat.className}>Name</div>,
            dataIndex: "name",
            key: "name",
            align: "center",
            className: `${montserrat.className}`,
            render: (name) => <div className="">{name}</div>,
        },
        {
            title: <div className={montserrat.className}>Email</div>,
            dataIndex: "email",
            key: "email",
            align: "center",
            className: `${montserrat.className}`,
            render: (email) => <div className="">{email}</div>,
        },
        {
            title: <div className={montserrat.className}>Phone Number</div>,
            dataIndex: "phoneNumber",
            key: "phoneNumber",
            align: "center",
            className: `${montserrat.className}`,
            render: (_phoneNumber, record) => (
                <div>
                    {record.countryCode ? `+${record.countryCode}` : ""}{" "}
                    {record.phoneNumber}
                </div>
            ),
        },
        {
            title: <div className={montserrat.className}>Address</div>,
            dataIndex: "address",
            key: "address",
            align: "center",
            className: `${montserrat.className}`,
            render: (address) => <div className="">{address}</div>,
        },
        {
            title: <div className={montserrat.className}>View</div>,
            dataIndex: "view",
            key: "view",
            align: "center",
            width: 70,
            className: `${montserrat.className}`,
            render: (_: any, record: any) => (
                <div className="flex justify-center">
                    <AiOutlineEye className="w-[25px] h-[25px] cursor-pointer" />
                </div>
            ),
        },
        {
            title: <div className={montserrat.className}>Action</div>,
            dataIndex: "more",
            key: "more",
            align: "center",
            className: "font-montserrat",
            width: 100,
            render: (_: any, record: any) => {
                const isActive = record.status === "ACTIVE";

                const handleClick = (actionType: "APPROVE" | "REJECT") => {
                    setRowCleanerRecord(record);
                    setModalOpen(true);
                    setActionType(actionType);
                };

                if (alignValue === "ALL") {
                    return record.isTakenAction ? (
                        <div className="flex justify-center">
                            <Switch
                                checked={isActive}
                                checkedChildren={<RxCheck className="text-[20px]" />}
                                unCheckedChildren={
                                    <RxCross2 className="text-[17px] relative top-[2px]" />
                                }
                                onChange={(checked) => {
                                    // handle toggle status here
                                }}
                            />
                        </div>
                    ) : (
                        <div className="flex justify-center gap-1">
                            <RxCheckCircled
                                className="w-[25px] h-[25px] cursor-pointer"
                                onClick={() => handleClick("APPROVE")}
                            />
                            <RxCrossCircled
                                className="w-[25px] h-[25px] cursor-pointer"
                                onClick={() => handleClick("REJECT")}
                            />
                        </div>
                    );
                }

                if (alignValue === "NEW") {
                    return (
                        <div className="flex justify-center gap-1">
                            <RxCheckCircled
                                className="w-[25px] h-[25px] cursor-pointer"
                                onClick={() => handleClick("APPROVE")}
                            />
                            <RxCrossCircled
                                className="w-[25px] h-[25px] cursor-pointer"
                                onClick={() => handleClick("REJECT")}
                            />
                        </div>
                    );
                }

                if (alignValue === "ACTIVE") {
                    return (
                        <div className="flex justify-center">
                            <RxCrossCircled
                                className="w-[25px] h-[25px] cursor-pointer"
                                onClick={() => handleClick("REJECT")}
                            />
                        </div>
                    );
                }

                if (alignValue === "INACTIVE") {
                    return (
                        <div className="flex justify-center">
                            <RxCheckCircled
                                className="w-[25px] h-[25px] cursor-pointer"
                                onClick={() => handleClick("APPROVE")}
                            />
                        </div>
                    );
                }

                return null;
            },
        },
    ];

    return (
        <div className="w-full h-full">
            <Table
                rowKey="id"
                columns={cleanerManagementColumns}
                dataSource={data}
                className={`w-full bg-white rounded-[10px] custom-table ${montserrat.className}`}
                style={{ backgroundColor: "transparent" }}
                pagination={{
                    pageSize: 10,
                    hideOnSinglePage: true,
                }}
                sticky
                locale={{
                    emptyText: (
                        <div
                            className={`h-[calc(100vh-280px)] flex items-center justify-center font-montserrat font-[500]`}
                        >
                            <Empty
                                description={`${search !== ""
                                        ? "No results found for your search."
                                        : "No data available"
                                    }`}
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                            />
                        </div>
                    ),
                }}
            />
            <ActionModal
                open={modalOpen}
                onClose={() => setModalOpen(false)}
                onSubmit={(reason) => {
                    // handle approve/reject with reason
                }}
                actionType={actionType}
            />
        </div>
    );
};

export default ManageCleanerTable;
