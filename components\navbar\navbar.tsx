"use client";
import React, { useState, useEffect } from "react";
import Image from "next/image";
import { Mont<PERSON>rat } from "next/font/google";
import { MenuOutlined } from "@ant-design/icons";
import { Avatar, Dropdown } from "antd";
import { useSidebarContext } from "@/src/context/sidebar.context";
import { usePathname, useRouter } from "next/navigation";
import { FaChevronDown } from "react-icons/fa";
import { TbLockPassword } from "react-icons/tb";
import { PiSignOutBold } from "react-icons/pi";

const montserrat = Montserrat({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const Navbar = () => {
    const pathName = usePathname();
    const router = useRouter();
    const { setIsCollapsed, setIsTabChangeLoading } = useSidebarContext();
    const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
    const [email, setEmail] = useState("");
    const [isOpen, setIsOpen] = useState(false);
    const [isMounted, setIsMounted] = useState(false);

    useEffect(() => {
        // Access localStorage safely in useEffect
        const email = localStorage.getItem("email");
        if (email) {
            setEmail(email);
        }

        const collapsed = localStorage.getItem("collapsed");
        if (collapsed)
            collapsed === "true" ? setIsCollapsed(true) : setIsCollapsed(false);

        // Set mounted state to true
        setIsMounted(true);
    }, []);

    const items: any = [
        {
            key: "0",
            label: (
                <h1
                    className={`text-[14px] py-1 cursor-default tracking-wide font-[500] truncate ${montserrat.className}`}
                >
                    {email || "<EMAIL>"}
                </h1>
            ),
        },
        {
            key: "1",
            label: (
                <div
                    onClick={() => isMounted && setIsLogoutModalOpen(true)}
                    className={`text-[15px] py-1 flex gap-2 items-center font-[500] ${montserrat.className}`}
                >
                    <TbLockPassword className="text-[18px]" />
                    Change Password
                </div>
            ),
        },
        {
            key: "2",
            label: (
                <div
                    onClick={() => isMounted && setIsLogoutModalOpen(true)}
                    className={`text-[15px] py-1 flex gap-2 items-center font-[500] ${montserrat.className}`}
                >
                    <PiSignOutBold className="text-[18px]" />
                    Sign Out
                </div>
            ),
        },
    ];

    useEffect(() => {
        const email = localStorage.getItem("email");
        if (email) {
            setEmail(email);
        }
    }, []);

    return (
        <div
            className={`w-full h-[65px] px-5 flex items-center justify-between bg-white ${montserrat.className}`}
            style={{ boxShadow: "0px 5px 20px 0px #0000000F" }}
        >
            <div className="flex gap-6 items-center">
                <MenuOutlined
                    className="text-xl cursor-pointer"
                    onClick={() => {
                        setIsCollapsed((prev: boolean) => {
                            if (prev) {
                                localStorage.setItem("collapsed", "false");
                            } else {
                                localStorage.setItem("collapsed", "true");
                            }
                            return !prev;
                        });
                    }}
                />
                <div
                    className="max-[912px]:hidden flex gap-4 items-center"
                    onClick={() => {
                        setIsTabChangeLoading(true);
                    }}
                >
                    <h3 className="max-[1110px]:hidden text-[20px] font-bold leading-[100%] text-center cursor-pointer">
                        {pathName === "/customers" ? "Customers" : "Cleaners"}
                    </h3>
                </div>
            </div>

            <div className="flex items-center">
                <Dropdown
                    className="hidden md:flex gap-3"
                    menu={{ items }}
                    trigger={["click"]}
                    placement="bottomRight"
                    onOpenChange={(visible) => setIsOpen(visible)}
                >
                    <span className="flex flex-row gap-1 items-center cursor-pointer w-full h-full">
                        <Avatar
                            style={{
                                backgroundColor: "#DBEAFE",
                                color: "#2563EB",
                                verticalAlign: "middle",
                                fontSize: "18px",
                            }}
                            className="font-bold"
                            size="large"
                        >
                            {email ? email.toUpperCase().slice(0, 2) : "A"}
                        </Avatar>
                        <FaChevronDown
                            className={`text-[12px] !font-[600] text-gray-600 transform transition-transform duration-300 ${
                                isOpen ? "rotate-180" : ""
                            }`}
                            style={{
                                fontSize: "9px",
                                fontWeight: "bold",
                                transform: "scaleX(1.4)",
                            }}
                        />
                    </span>
                </Dropdown>
            </div>

            {/* <LogoutModal
                isModalOpen={isLogoutModalOpen}
                setIsModalOpen={setIsLogoutModalOpen}
            /> */}
        </div>
    );
};

export default Navbar;
