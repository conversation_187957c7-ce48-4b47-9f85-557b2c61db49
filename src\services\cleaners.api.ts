import { fetch } from "@/src/libs/helpers";

export interface cleanersQueryParams {
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    type?: string;
    grade?: string;
    level?: string;
    search_column?: string[];
    categoryId?: string;
    customerId?: string;
    status?: string;
}

export interface cleanersBody {
    category?: string;
    categoryId?: string;
    mainTitle?: string;
    subTitle?: string;
    image?: string;
    description?: string;
    customerId?: string;
    website?: string;
    status?: string;
    shortDescription?: string,
    newPosition?: number,
}

export const getCleanersList = async (
    params: cleanersQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/cleaners`,
        method: "GET",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

