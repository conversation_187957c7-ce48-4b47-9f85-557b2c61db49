@import "tailwindcss";

:root {
  --background: #f3f4f6; /* light gray (<PERSON>l<PERSON>'s gray-100) */
  --foreground: #171717;
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --font-sans: var(--font-geist-sans);
  --font-mono: var(--font-geist-mono);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background: #f3f4f6;
    --foreground: #ededed;
  }
}

body {
  background: var(--background);
  color: var(--foreground);
  font-family: Arial, Helvetica, sans-serif;
}

/* custom scrollbars */
.scrollbar::-webkit-scrollbar {
    width: 5px;
    height: 100%;
}

.scrollbar::-webkit-scrollbar-thumb {
    background-color: #d1d1d1c1;
    border-radius: 3px;
}

.scrollbar::-webkit-scrollbar-thumb:hover {
    background-color: #ababab;
}

.scrollbar-mini::-webkit-scrollbar {
    width: 2px;
    height: 100%;
}

.scrollbar-mini::-webkit-scrollbar-thumb {
    background-color: #d1d1d18f;
    border-radius: 3px;
}

.scrollbar-mini::-webkit-scrollbar-thumb:hover {
    background-color: #ababab;
}

.scrollbar-hide::-webkit-scrollbar {
    width: 0px;
    height: 100%;
}

/* Antd Segmented */
.custom-segmented .ant-segmented-item-label {
    width: 150px;
}

.custom-segmented {
  background-color: #DBEAFE !important;
  border-radius: 15px;
  padding: 4px;
}
.custom-segmented-btn {
  background: transparent;
  color: #1e293b;
  border: none;
  cursor: pointer;
}
.custom-segmented-btn.selected {
  background: linear-gradient(90deg, #3B82F6 0%, #60A5FA 100%);
  color: #fff !important;
}
.custom-segmented-btn:hover:not(.selected) {
  background-color: #BFDBFE !important;
  color: #1D4ED8 !important;
}

/* Responsive Table Styles */
.custom-table .ant-table-container {
  overflow-x: auto;
}

.custom-table .ant-table-thead > tr > th.ant-table-cell-fix-left,
.custom-table .ant-table-tbody > tr > td.ant-table-cell-fix-left {
  background: #fff !important;
  z-index: 2;
}

.custom-table .ant-table-thead > tr > th.ant-table-cell-fix-right,
.custom-table .ant-table-tbody > tr > td.ant-table-cell-fix-right {
  background: #fff !important;
  z-index: 2;
}

.custom-table .ant-table-thead > tr > th.ant-table-cell-fix-left::after,
.custom-table .ant-table-tbody > tr > td.ant-table-cell-fix-left::after {
  box-shadow: inset 10px 0 8px -8px rgba(0, 0, 0, 0.15);
}

.custom-table .ant-table-thead > tr > th.ant-table-cell-fix-right::before,
.custom-table .ant-table-tbody > tr > td.ant-table-cell-fix-right::before {
  box-shadow: inset -10px 0 8px -8px rgba(0, 0, 0, 0.15);
}

/* Ensure proper scrolling on mobile */
@media (max-width: 768px) {
  .custom-table .ant-table-container {
    overflow-x: scroll;
    -webkit-overflow-scrolling: touch;
  }

  .custom-table .ant-table-body {
    min-width: 100%;
  }
}