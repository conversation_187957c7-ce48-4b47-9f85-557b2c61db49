"use client";
import React, { useState } from "react";
import Image from "next/image";
import { Montserrat } from "next/font/google";
import { Input, Button } from "antd";
import { MdOutlineMailOutline } from "react-icons/md";
import { MdOutlineLock } from "react-icons/md";
import { useRouter } from "next/navigation";

const montserrat = Montserrat({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const Login = () => {
    const [email, setEmail] = useState("");
    const [password, setPassword] = useState("");
    const [emailError, setEmailError] = useState("");
    const [passwordError, setPasswordError] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const router = useRouter();

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Handle login logic here
        console.log("Login attempt:", { email, password });
    };
    return (
        <div
            className={`flex flex-col w-screen h-screen relative text-black select-none ${montserrat.className}`}
        >
            {/* Top half - blue gradient */}
            <div className="h-[65%] md:h-[60%] w-full bg-gradient-to-br from-blue-500 to-blue-600" />

            {/* Bottom half - white */}
            <div className="h-[35%] md:h-[40%] w-full bg-white" />

            {/* Login Form */}
            <div className="absolute w-full h-full flex justify-center items-center px-2 sm:px-4">
                <div
                    className="bg-white rounded-2xl w-full max-w-[95vw] sm:max-w-[80vw] md:min-w-[42%] md:max-w-[480px] flex flex-col justify-center items-center px-4 sm:px-8 md:px-10 py-8 sm:py-12 md:py-16 shadow-lg"
                    style={{
                        boxShadow: "0 2px 6px rgba(0, 0, 0, 0.3)",
                    }}
                >
                    <Image
                        src={`/images/logo.png`}
                        width={180}
                        height={72}
                        alt="logo"
                        className="w-[140px] sm:w-[180px] h-auto"
                    />
                    <h2 className="text-[#0085D7] text-[24px] sm:text-[28px] md:text-[32px] pt-4 sm:pt-6 pb-6 sm:pb-10 font-[700] text-center">
                        Log In
                    </h2>

                    {/* Form */}
                    <form
                        onSubmit={handleSubmit}
                        className="w-full flex flex-col gap-6 sm:gap-8 md:gap-10"
                    >
                        {/* Email Field */}
                        <Input
                            type="email"
                            id="email"
                            value={email}
                            onChange={(e) => {
                                setEmailError("");
                                setEmail(e.target.value);
                            }}
                            className={`w-full shadow-lg h-[40px] focus:shadow-lg transition-shadow duration-200 !border-none !outline-none py-2 sm:py-3 px-3 sm:px-4 rounded-lg text-base text-gray-700 placeholder-gray-400  ${montserrat.className}`}
                            placeholder="Email ID"
                            prefix={
                                <MdOutlineMailOutline className="text-gray-600 w-[20px] h-[20px] mr-2 sm:mr-3" />
                            }
                            required
                        />
                        {emailError && (
                            <p className="text-red-500 text-[12px] absolute ml-1">
                                {emailError}
                            </p>
                        )}

                        {/* Password Field */}
                        <div className="relative w-full">
                            <Input
                                type={showPassword ? "text" : "password"}
                                value={password}
                                onChange={(e) => {
                                    setPasswordError("");
                                    setPassword(e.target.value);
                                }}
                                className={`w-full shadow-lg h-[40px] focus:shadow-lg transition-shadow duration-200 !border-none !outline-none py-2 sm:py-3 px-3 sm:px-4 rounded-lg text-base text-gray-700 placeholder-gray-400 ${montserrat.className}`}
                                placeholder="Password"
                                prefix={
                                    <MdOutlineLock className="text-gray-600 w-[20px] h-[20px] mr-2 sm:mr-3" />
                                }
                                required
                            />
                            <button
                                type="button"
                                onClick={() => setShowPassword((prev) => !prev)}
                                className="absolute right-3 sm:right-4 top-1/2 -translate-y-1/2 text-gray-500 z-20"
                            >
                                {showPassword ? (
                                    <img
                                        src="/images/login/eye.svg"
                                        alt="visibility"
                                        className="w-[20px] h-[20px] cursor-pointer"
                                    />
                                ) : (
                                    <img
                                        src="/images/login/eye-slash.svg"
                                        alt="visibility"
                                        className="w-[20px] h-[20px] cursor-pointer"
                                    />
                                )}
                            </button>
                        </div>
                        {passwordError && (
                            <p className="text-red-500 text-[12px] absolute ml-1">
                                {passwordError}
                            </p>
                        )}

                        {/* Forgot Password Link */}
                        <div className="w-full text-center">
                            <p
                                className={`text-blue-500 hover:text-blue-600 text-[14px] sm:text-[16px] font-[700] cursor-pointer ${montserrat.className}`}
                                onClick={() => router.push("/forgot-password")}
                            >
                                Forgot Password?
                            </p>
                        </div>

                        {/* Submit Button */}
                        <div className="w-full text-center">
                            <Button
                                type="primary"
                                className={`w-full sm:w-[220px] md:w-[300px] !h-[42px] sm:!h-[45px] !rounded-2xl !text-[18px] sm:!text-[20px] bg-blue-500 hover:bg-blue-600 text-white font-[600] py-2 sm:py-3 px-3 sm:px-4 transition duration-200 ease-in-out transform hover:scale-[0.96] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${montserrat.className}`}
                            >
                                LOG IN
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default Login;
