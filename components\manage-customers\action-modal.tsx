import React, { useState } from "react";
import { Button, Input, Modal, message } from "antd";
import { Montserrat } from "next/font/google";

const montserrat = Montserrat({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

interface ActionModalProps {
    open: boolean;
    onClose: () => void;
    onSubmit: (reason: string) => void;
    actionType: "APPROVE" | "REJECT";
}

const ActionModal: React.FC<ActionModalProps> = ({
    open,
    onClose,
    onSubmit,
    actionType,
}) => {
    const [reason, setReason] = useState("");
    const [loading, setLoading] = useState(false);

    const handleAction = () => {
        if (!reason.trim()) {
            message.error("Please provide a reason.");
            return;
        }
        setLoading(true);
        onSubmit(reason.trim());
        setLoading(false);
        setReason("");
        onClose();
    };

    return (
        <Modal
            title={
                <div
                    className={`text-blue-500 text-[20px] font-[600] text-start ${montserrat.className}`}
                >
                    {actionType === "APPROVE"
                        ? "Approve Customer"
                        : "Reject Customer"}
                </div>
            }
            open={open}
            onCancel={onClose}
            footer={null}
            className={`rounded-lg border-black w-[400px] h-auto`}
            centered
            width={400}
            closable={false}
            maskClosable={false}
        >
            <div className="mb-4">
                <label className="text-[14px] text-[#878787] font-[400]">
                    Reason for{" "}
                    {actionType === "APPROVE" ? "approval" : "rejection"}
                </label>
                <Input.TextArea
                    autoSize={{ minRows: 3, maxRows: 5 }}
                    value={reason}
                    onChange={(e) => setReason(e.target.value)}
                    placeholder={`Enter reason for ${
                        actionType === "APPROVE" ? "approval" : "rejection"
                    }`}
                    className={`mt-2 !border-2 !border-blue-100 ${montserrat.className}`}
                />
            </div>
            <div className="flex justify-end space-x-3">
                <Button
                    onClick={onClose}
                    className={`${montserrat.className} bg-transparent text-[#67A1A3] border-[1px] border-[#67A1A3] text-[16px] w-[30%] h-[40px] font-[500] rounded-xl`}
                >
                    Cancel
                </Button>
                <Button
                    type="primary"
                    loading={loading}
                    onClick={handleAction}
                    className={`${montserrat.className}  text-white text-[16px] w-[30%] h-[40px] font-[500] rounded-xl`}
                >
                    {actionType === "APPROVE" ? "Approve" : "Reject"}
                </Button>
            </div>
        </Modal>
    );
};

export default ActionModal;
