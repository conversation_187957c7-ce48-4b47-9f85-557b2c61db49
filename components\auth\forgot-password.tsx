"use client";
import React, { useState } from "react";
import Image from "next/image";
import { Montser<PERSON> } from "next/font/google";
import { Input, Button } from "antd";
import { MdOutlineMailOutline } from "react-icons/md";
import { useRouter } from "next/navigation";

const montserrat = Montserrat({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const ForgotPassword = () => {
    const [email, setEmail] = useState("");
    const [emailError, setEmailError] = useState("");
    const router = useRouter();

    const handleSubmit = (e: React.FormEvent) => {
        e.preventDefault();
        // Handle login logic here
        console.log("Forgot Password attempt:", { email });
    };
    return (
        <div
            className={`flex flex-col w-screen h-screen relative text-black select-none ${montserrat.className}`}
        >
            {/* Top half - blue gradient */}
            <div className="h-[65%] md:h-[60%] w-full bg-gradient-to-br from-blue-500 to-blue-600" />

            {/* Bottom half - white */}
            <div className="h-[35%] md:h-[40%] w-full bg-white" />

            {/* Forgot Password Form */}
            <div className="absolute w-full h-full flex justify-center items-center px-2 sm:px-4">
                <div
                    className="bg-white rounded-2xl w-full max-w-[95vw] sm:max-w-[80vw] md:min-w-[42%] md:max-w-[480px] flex flex-col justify-center items-center px-4 sm:px-8 md:px-10 py-8 sm:py-12 md:py-16 shadow-lg"
                    style={{
                        boxShadow: "0 2px 6px rgba(0, 0, 0, 0.3)",
                    }}
                >
                    <Image
                        src={`/images/logo.png`}
                        width={180}
                        height={72}
                        alt="logo"
                        className="w-[140px] sm:w-[180px] h-auto mb-8"
                    />
                    <Image
                        src={`/images/auth/forgot-password.png`}
                        width={180}
                        height={72}
                        alt="forgot-password"
                        className="w-[48px] sm:w-[50px] h-auto"
                    />
                    <h2 className="text-[#0085D7] text-[24px] sm:text-[28px] md:text-[32px] pt-1 sm:pt-2 pb-2 font-[700] text-center">
                        Forgot Password
                    </h2>
                    <p className="text-[13px] sm:text-[14px] pb-6 sm:pb-10 text-center">
                        In order to retrieve your password, please enter
                        registered email id.
                    </p>

                    {/* Form */}
                    <form
                        onSubmit={handleSubmit}
                        className="w-full flex flex-col gap-6 sm:gap-8 md:gap-10"
                    >
                        {/* Email Field */}
                        <Input
                            type="email"
                            id="email"
                            value={email}
                            onChange={(e) => {
                                setEmailError("");
                                setEmail(e.target.value);
                            }}
                            className={`w-full shadow-lg h-[40px] focus:shadow-lg transition-shadow duration-200 !border-none !outline-none py-2 sm:py-3 px-3 sm:px-4 rounded-lg text-base text-gray-700 placeholder-gray-400  ${montserrat.className}`}
                            placeholder="Email ID"
                            prefix={
                                <MdOutlineMailOutline className="text-gray-600 w-[20px] h-[20px] mr-2 sm:mr-3" />
                            }
                            required
                        />
                        {emailError && (
                            <p className="text-red-500 text-[12px] absolute ml-1">
                                {emailError}
                            </p>
                        )}

                        {/* Back to Login Link */}
                        <div className="w-full text-center">
                            <p
                                className={`text-blue-500 hover:text-blue-600 text-[14px] sm:text-[16px] font-[700] cursor-pointer ${montserrat.className}`}
                                onClick={() => router.push("/sign-in")}
                            >
                                Back to LOG IN?
                            </p>
                        </div>

                        {/* Submit Button */}
                        <div className="w-full text-center">
                            <Button
                                type="primary"
                                className={`w-full sm:w-[220px] md:w-[300px] !h-[42px] sm:!h-[45px] !rounded-2xl !text-[18px] sm:!text-[20px] bg-blue-500 hover:bg-blue-600 text-white font-[600] py-2 sm:py-3 px-3 sm:px-4 transition duration-200 ease-in-out transform hover:scale-[0.96] focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 ${montserrat.className}`}
                            >
                                SUBMIT
                            </Button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    );
};

export default ForgotPassword;
