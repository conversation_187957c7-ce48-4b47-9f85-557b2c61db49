"use client";
import { usePathname, useRouter } from "next/navigation";
import React, { useCallback, useEffect, useState } from "react";
import Navbar from "../navbar/navbar";
import Sidebar from "../sidebar/sidebar";
import { useSidebarContext } from "@/src/context/sidebar.context";

const NAVBAR_HEIGHT = 70; // Navbar height in pixels
const SIDEBAR_WIDTH = 230; // Sidebar width in pixels

const fullPagePaths = ["/sign-in", "/forgot-password"];
const appPagePaths = [
    "/customers",
    "/cleaners",
    "/bookings",
    "/refunds",
    "/aprons",
    "/help-and-support",
    "/settings",
];

const IndexLayout = ({ children }: { children: React.ReactNode }) => {
    const router = useRouter();
    const pathName = usePathname();

    const { isCollapsed, isTabChangeLoading, setIsTabChangeLoading } =
        useSidebarContext();

    const [height, setHeight] = useState(0);

    const handleResize = useCallback(() => setHeight(window.innerHeight), []);

    useEffect(() => {
        // Set initial height
        setHeight(window.innerHeight);

        // Add resize event listener
        window.addEventListener("resize", handleResize);

        // Cleanup event listener on unmount
        return () => window.removeEventListener("resize", handleResize);
    }, [handleResize]);

    useEffect(() => {
        setTimeout(() => {
            setIsTabChangeLoading(false);
        }, 1000);
    }, [pathName]);

    if (appPagePaths.some((path) => pathName.startsWith(path))) {
        return (
            <div
                className="w-[100vw] !tracking-wide flex text-black"
                style={{
                    height: `${height}px`,
                }}
            >
                <Sidebar />
                <div
                    className={`${
                        isCollapsed
                            ? "lg:w-[calc(100%-68px)]"
                            : "lg:w-[calc(100%-270px)]"
                    } transition-all duration-500 h-full`}
                >
                    <Navbar />
                    <div
                        className={`w-full transition-all duration-500 overflow-y-auto scrollbar`}
                        style={{
                            height: `calc(${height}px - ${NAVBAR_HEIGHT}px)`,
                        }}
                    >
                        {isTabChangeLoading ? <></> : children}
                    </div>
                </div>
            </div>
        );
    }

    return children;
};

export default IndexLayout;
