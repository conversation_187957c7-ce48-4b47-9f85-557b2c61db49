"use client";
import React, { useEffect, useState } from "react";
import { Mont<PERSON>rat } from "next/font/google";
import { usePathname } from "next/navigation";
import Link from "next/link";
import { useSidebarContext } from "@/src/context/sidebar.context";
import { Tooltip } from "antd";
import { FaUsers } from "react-icons/fa";
import { SiCcleaner } from "react-icons/si";
import { LuTicketX } from "react-icons/lu";
import { RiRefundFill } from "react-icons/ri";
import { SiGoogledataproc } from "react-icons/si";
import { MdSupportAgent } from "react-icons/md";
import { IoSettingsOutline } from "react-icons/io5";
import Image from "next/image";

const montserrat = Montserrat({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

const Sidebar = () => {
    const path = usePathname();
    const [isLogoutModalOpen, setIsLogoutModalOpen] = useState(false);
    const [selectedItem, setSelectedItem] = useState("");
    const [email, setEmail] = useState("");
    const { isCollapsed, setIsTabChangeLoading } = useSidebarContext();

    useEffect(() => {
        const email = localStorage.getItem("email");
        if (email) {
            setEmail(email);
        }
    }, []);

    useEffect(() => {
        const page = path.split("/")[1];

        if (page && page !== "") {
            setSelectedItem(page);
        }
    }, [isCollapsed, path]);

    const handleButtonClick = (button: any) => {
        setSelectedItem(button);
        if (selectedItem !== button || path.split("/")[1] !== button) {
            setIsTabChangeLoading(true);
        }
    };

    if (!isCollapsed) {
        return (
            <div
                className={`absolute lg:relative shadow-xl lg:shadow-none bg-white z-50 w-[270px] h-full pb-40 lg:pb-0 overflow-y-auto scrollbar-hide ${montserrat.className} lg:transition-all duration-500 ease-in-out`}
                // data-aos="fade-right"
            >
                {/* Logo */}
                <Link
                    href={"/customers"}
                    className={`w-full transition-all flex items-center gap-4 cursor-pointer px-3 py-2 my-5`}
                    onClick={() => {
                        handleButtonClick("customers");
                    }}
                    // data-aos="fade-right"
                >
                    <Image
                        src={`/images/logo.png`}
                        alt="QleanOn Logo"
                        width={200}
                        height={180}
                        className="w-[220px] h-auto"
                    />
                </Link>

                <div className="flex flex-col gap-2 my-6">
                    {/* Manage Customers */}
                    <Link
                        href={"/customers"}
                        className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("customers");
                        }}
                        // data-aos="fade-right"
                    >
                        {selectedItem === "customers" ? (
                            <FaUsers className="text-blue-500" />
                        ) : (
                            <FaUsers className="text-[#919191]" />
                        )}
                        <p
                            className={`${
                                selectedItem === "customers"
                                    ? "text-blue-500 font-[700]"
                                    : "text-[#919191] font-[500]"
                            } text-[14px] leading-5 truncate`}
                        >
                            Manage Customers
                        </p>
                    </Link>

                    {/*  Manage Cleaners */}
                    <Link
                        href={"/cleaners"}
                        className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("cleaners");
                        }}
                        // data-aos="fade-right"
                    >
                        {selectedItem === "cleaners" ? (
                            <SiCcleaner className="text-blue-500" />
                        ) : (
                            <SiCcleaner className="text-[#919191]" />
                        )}
                        <p
                            className={`${
                                selectedItem === "cleaners"
                                    ? "text-blue-500 font-[700]"
                                    : "text-[#919191] font-[500]"
                            } text-[14px] leading-5 truncate`}
                        >
                            Manage Cleaners
                        </p>
                    </Link>

                    {/* Manage Bookings */}
                    <Link
                        href={"/bookings"}
                        className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("bookings");
                        }}
                        // data-aos="fade-right"
                    >
                        {selectedItem === "bookings" ? (
                            <LuTicketX className="text-blue-500" />
                        ) : (
                            <LuTicketX className="text-[#919191]" />
                        )}
                        <p
                            className={`${
                                selectedItem === "bookings"
                                    ? "text-blue-500 font-[700]"
                                    : "text-[#919191] font-[500]"
                            } text-[14px] leading-5 truncate`}
                        >
                            Manage Bookings
                        </p>
                    </Link>

                    {/* Manage Refunds */}
                    <Link
                        href={"/refunds"}
                        className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("refunds");
                        }}
                        // data-aos="fade-right"
                    >
                        {selectedItem === "refunds" ? (
                            <RiRefundFill className="text-blue-500" />
                        ) : (
                            <RiRefundFill className="text-[#919191]" />
                        )}
                        <p
                            className={`${
                                selectedItem === "refunds"
                                    ? "text-blue-500 font-[700]"
                                    : "text-[#919191] font-[500]"
                            } text-[14px] leading-5 truncate`}
                        >
                            Manage Refunds
                        </p>
                    </Link>

                    {/* Manage Aprons */}
                    <Link
                        href={"/aprons"}
                        className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("aprons");
                        }}
                        // data-aos="fade-right"
                    >
                        {selectedItem === "aprons" ? (
                            <SiGoogledataproc className="text-blue-500" />
                        ) : (
                            <SiGoogledataproc className="text-[#919191]" />
                        )}
                        <p
                            className={`${
                                selectedItem === "aprons"
                                    ? "text-blue-500 font-[700]"
                                    : "text-[#919191] font-[500]"
                            } text-[14px] leading-5 truncate`}
                        >
                            Manage Aprons
                        </p>
                    </Link>

                    {/* Help & Support */}
                    <Link
                        href={"/help-and-support"}
                        className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("help-and-support");
                        }}
                        // data-aos="fade-right"
                    >
                        {selectedItem === "help-and-support" ? (
                            <MdSupportAgent className="text-blue-500" />
                        ) : (
                            <MdSupportAgent className="text-[#919191]" />
                        )}
                        <p
                            className={`${
                                selectedItem === "help-and-support"
                                    ? "text-blue-500 font-[700]"
                                    : "text-[#919191] font-[500]"
                            } text-[14px] leading-5 truncate`}
                        >
                            Help & Support
                        </p>
                    </Link>

                    {/* Settings */}
                    <Link
                        href={"/settings"}
                        className={`w-full transition-all flex items-center gap-4 cursor-pointer px-8 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("settings");
                        }}
                        // data-aos="fade-right"
                    >
                        {selectedItem === "settings" ? (
                            <IoSettingsOutline className="text-blue-500" />
                        ) : (
                            <IoSettingsOutline className="text-[#919191]" />
                        )}
                        <p
                            className={`${
                                selectedItem === "settings"
                                    ? "text-blue-500 font-[700]"
                                    : "text-[#919191] font-[500]"
                            } text-[14px] leading-5 truncate`}
                        >
                            Settings
                        </p>
                    </Link>
                </div>
            </div>
        );
    }

    return (
        <div
            className={`absolute lg:relative w-0 lg:w-[100px] h-full bg-white ${montserrat.className} transition-all duration-500 ease-in-out`}
        >
            <Link
                href={"/customers"}
                className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 my-5`}
                onClick={() => {
                    handleButtonClick("customers");
                }}
            >
                <Image
                    src={`/images/favicon/favicon.png`}
                    alt="QleanOn Logo"
                    width={50}
                    height={50}
                    className="w-[60px] h-auto"
                />
            </Link>

            <div className="hidden lg:flex flex-col gap-2 my-6">
                {/* Manage Customers */}
                <Tooltip title="Manage Customers" placement="right">
                    <Link
                        href={"/customers"}
                        className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("customers");
                        }}
                    >
                        {selectedItem === "customers" ? (
                            <FaUsers className="text-blue-500 " />
                        ) : (
                            <FaUsers className="text-[#919191]" />
                        )}
                    </Link>
                </Tooltip>

                {/* Manage Cleaners */}
                <Tooltip title="Manage Cleaners" placement="right">
                    <Link
                        href={"/cleaners"}
                        className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("cleaners");
                        }}
                    >
                        {selectedItem === "cleaners" ? (
                            <SiCcleaner className="text-blue-500" />
                        ) : (
                            <SiCcleaner className="text-[#919191]" />
                        )}
                    </Link>
                </Tooltip>

                {/* Manage Bookings */}
                <Tooltip title="Manage Bookings" placement="right">
                    <Link
                        href={"/bookings"}
                        className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("bookings");
                        }}
                    >
                        {selectedItem === "bookings" ? (
                            <LuTicketX className="text-blue-500" />
                        ) : (
                            <LuTicketX className="text-[#919191]" />
                        )}
                    </Link>
                </Tooltip>

                {/* Manage Refunds */}
                <Tooltip title="Manage Refunds" placement="right">
                    <Link
                        href={"/refunds"}
                        className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("refunds");
                        }}
                    >
                        {selectedItem === "refunds" ? (
                            <RiRefundFill className="text-blue-500" />
                        ) : (
                            <RiRefundFill className="text-[#919191]" />
                        )}
                    </Link>
                </Tooltip>

                {/* Manage Aprons */}
                <Tooltip title="Manage Aprons" placement="right">
                    <Link
                        href={"/aprons"}
                        className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("aprons");
                        }}
                    >
                        {selectedItem === "aprons" ? (
                            <SiGoogledataproc className="text-blue-500" />
                        ) : (
                            <SiGoogledataproc className="text-[#919191]" />
                        )}
                    </Link>
                </Tooltip>

                {/* Help & Support */}
                <Tooltip title="Help & Support" placement="right">
                    <Link
                        href={"/help-and-support"}
                        className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("help-and-support");
                        }}
                    >
                        {selectedItem === "help-and-support" ? (
                            <MdSupportAgent className="text-blue-500" />
                        ) : (
                            <MdSupportAgent className="text-[#919191]" />
                        )}
                    </Link>
                </Tooltip>

                {/* Settings */}
                <Tooltip title="Settings" placement="right">
                    <Link
                        href={"/settings"}
                        className={`w-full transition-all flex items-center justify-center gap-4 cursor-pointer px-0 py-2 hover:bg-blue-100 text-3xl`}
                        onClick={() => {
                            handleButtonClick("settings");
                        }}
                    >
                        {selectedItem === "settings" ? (
                            <IoSettingsOutline className="text-blue-500" />
                        ) : (
                            <IoSettingsOutline className="text-[#919191]" />
                        )}
                    </Link>
                </Tooltip>
            </div>
        </div>
    );
};

export default Sidebar;
