"use client";
import React, { useState } from "react";
import { Switch, Empty, MenuProps, Pagination, Table, Button } from "antd";
import { Montserrat } from "next/font/google";
import { ColumnsType } from "antd/es/table";
import ActionModal from "./action-modal";
import { RxCrossCircled } from "react-icons/rx";
import { RxCheckCircled } from "react-icons/rx";
import { RxCross2 } from "react-icons/rx";
import { RxCheck } from "react-icons/rx";
import { AiOutlineEye } from "react-icons/ai";

import Image from "next/image";

const montserrat = Montserrat({
    weight: ["100", "300", "400", "500", "700"],
    subsets: ["latin"],
});

const ManageCustomerTable = ({
    data,
    fetch,
    pagination,
    fetchAllCustomereDataList,
    alignValue,
    search,
}: any) => {
    const [rowCustomerRecord, setRowCustomerRecord] = useState("");
    const [modalOpen, setModalOpen] = useState(false);
    const [actionType, setActionType] = useState<"APPROVE" | "REJECT">(
        "REJECT"
    );

    // Table Columns
    const customerManagementColumns: ColumnsType<any> = [
        {
            title: <div className={montserrat.className}>Image</div>,
            dataIndex: "profile",
            key: "profile",
            align: "center",
            className: montserrat.className,
            render: (profile: string, record: any) => {
                return (
                    <div className="flex justify-center items-center">
                        {profile ? (
                            <img
                                height={48}
                                width={48}
                                alt="Profile Picture"
                                src={profile}
                                className="rounded-full object-cover"
                            />
                        ) : record.name ? (
                            <div className="w-12 h-12 rounded-full bg-gray-200 flex items-center justify-center text-[18px] font-semibold">
                                {record.name.charAt(0).toUpperCase()}
                            </div>
                        ) : (
                            <img
                                src="/images/profile-circle.svg"
                                alt="Profile"
                                width={48}
                                height={48}
                            />
                        )}
                    </div>
                );
            },
        },
        {
            title: <div className={montserrat.className}>Name</div>,
            dataIndex: "name",
            key: "name",
            align: "center",
            className: `${montserrat.className}`,
            render: (name) => <div className="">{name ? name : ""}</div>,
        },
        {
            title: <div className={montserrat.className}>Email</div>,
            dataIndex: "email",
            key: "email",
            align: "center",
            className: `${montserrat.className}`,
            render: (email) => <div className="">{email ? email : ""}</div>,
        },
        {
            title: <div className={montserrat.className}>Phone Number</div>,
            dataIndex: "phoneNumber",
            key: "phoneNumber",
            align: "center",
            className: `${montserrat.className}`,
            render: (_phoneNumber, record) => (
                <div>
                    {record.countryCode ? `${record.countryCode}` : ""}{" "}
                    {record.phone}
                </div>
            ),
        },
        {
            title: <div className={montserrat.className}>Address</div>,
            dataIndex: "address",
            key: "address",
            align: "center",
            className: `${montserrat.className}`,
            render: (address) => (
                <div className="">{address ? address : ""}</div>
            ),
        },
        {
            title: <div className={montserrat.className}>View</div>,
            dataIndex: "view",
            key: "view",
            align: "center",
            width: 100,
            className: `${montserrat.className}`,
            render: (_: any, record: any) => (
                <div className="flex justify-center">
                    <AiOutlineEye className="w-[25px] h-[25px] cursor-pointer" />
                </div>
            ),
        },
        {
            title: <div className={montserrat.className}>Action</div>,
            dataIndex: "more",
            key: "more",
            align: "center",
            className: "font-montserrat",
            width: 100,
            render: (_: any, record: any) => {
                const isActive = record.status === "ACTIVE";

                const handleClick = (actionType: "APPROVE" | "REJECT") => {
                    setRowCustomerRecord(record);
                    setModalOpen(true);
                    setActionType(actionType);
                };

                if (alignValue === "ALL") {
                    return record.isTakenAction ? (
                        <div className="flex justify-center">
                            <Switch
                                checked={isActive}
                                checkedChildren={
                                    <RxCheck className="text-[20px]" />
                                }
                                unCheckedChildren={
                                    <RxCross2 className="text-[17px] relative top-[2px]" />
                                }
                                onChange={(checked) => {
                                    // handle toggle status here
                                }}
                            />
                        </div>
                    ) : (
                        <div className="flex justify-center gap-1">
                            <RxCheckCircled
                                className="w-[25px] h-[25px] cursor-pointer"
                                onClick={() => handleClick("APPROVE")}
                            />
                            <RxCrossCircled
                                className="w-[25px] h-[25px] cursor-pointer"
                                onClick={() => handleClick("REJECT")}
                            />
                        </div>
                    );
                }

                if (alignValue === "ACTIVE") {
                    return (
                        <div className="flex justify-center">
                            <RxCrossCircled
                                className="w-[25px] h-[25px] cursor-pointer"
                                onClick={() => handleClick("REJECT")}
                            />
                        </div>
                    );
                }

                if (alignValue === "INACTIVE") {
                    return (
                        <div className="flex justify-center">
                            <RxCheckCircled
                                className="w-[25px] h-[25px] cursor-pointer"
                                onClick={() => handleClick("APPROVE")}
                            />
                        </div>
                    );
                }

                return null;
            },
        },
    ];

    return (
        <div>
            <Table
                rowKey="id"
                columns={customerManagementColumns}
                dataSource={data}
                pagination={false}
                className={`w-full bg-white rounded-[10px] custom-table ${montserrat.className}`}
                style={{ backgroundColor: "transparent" }}
                footer={() =>
                    data.length > 0 && (
                        <div
                            className={`flex justify-between items-center px-2 bg-transparent text-[#44444F] rounded-b-[10px] ${montserrat.className}`}
                            style={{ backgroundColor: "transparent" }}
                        >
                            {/* Record Count */}
                            <span className="text-[14px] leading-[24px] font-[400]">{`${
                                pagination.pageSize * (pagination.current - 1) +
                                1
                            } - ${Math.min(
                                pagination.pageSize * pagination.current,
                                pagination.total
                            )} of ${pagination.total} ${
                                pagination.total > 1 ? "items" : "item"
                            }`}</span>
                            {/* Pagination */}
                            <div className="pagination-wrapper">
                                <Pagination
                                    current={pagination.current}
                                    pageSize={pagination.pageSize}
                                    total={pagination.total}
                                    hideOnSinglePage={true}
                                    onChange={(page) => fetch(page)}
                                    showSizeChanger={false}
                                    itemRender={(
                                        page,
                                        type,
                                        originalElement
                                    ) => {
                                        if (type === "prev") {
                                            return (
                                                <div className="border border-[#92929D] text-[#92929D] w-[32px] h-[32px] flex items-center justify-center rounded-[8px]">
                                                    <Image
                                                        width={24}
                                                        height={24}
                                                        className="w-[24px] h-auto relative"
                                                        src="/images/table/page-left.svg"
                                                        alt=""
                                                    />
                                                </div>
                                            );
                                        }
                                        if (type === "next") {
                                            return (
                                                <div className="border border-[#92929D] text-[#92929D] w-[32px] h-[32px] flex items-center justify-center rounded-[8px]">
                                                    <Image
                                                        width={24}
                                                        height={24}
                                                        className="w-[24px] h-auto"
                                                        src="/images/table/page-right.svg"
                                                        alt=""
                                                    />
                                                </div>
                                            );
                                        }
                                        if (type === "page") {
                                            return (
                                                <div
                                                    className={`w-[32px] h-[32px] flex items-center justify-center rounded-md border-none outline-none ${
                                                        page ===
                                                        pagination.current
                                                            ? "text-[#121212] font-bold border-none outline-none"
                                                            : "text-[#92929D]"
                                                    }`}
                                                    style={{
                                                        border: "none",
                                                    }}
                                                >
                                                    {page}
                                                </div>
                                            );
                                        }
                                        return originalElement;
                                    }}
                                    className="custom-pagination"
                                />
                            </div>
                        </div>
                    )
                }
                sticky
                locale={{
                    emptyText: (
                        <div
                            className={`h-[calc(100vh-280px)] flex items-center justify-center font-montserrat font-[500]`}
                        >
                            <Empty
                                description={`${
                                    search !== ""
                                        ? "No results found for your search."
                                        : "No data available"
                                }`}
                                image={Empty.PRESENTED_IMAGE_SIMPLE}
                            />
                        </div>
                    ),
                }}
            />
            <ActionModal
                open={modalOpen}
                onClose={() => setModalOpen(false)}
                onSubmit={(reason) => {
                    // handle approve/reject with reason
                }}
                actionType={actionType}
            />
        </div>
    );
};

export default ManageCustomerTable;
