"use client";
import React, { useCallback, useState } from "react";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { CLEANERS_TABS } from "@/src/libs/constants";
import { LuSearch } from "react-icons/lu";
import { debounce } from "lodash";
import Loader from "../common/Loader";
import ManageCleanerTable from "./cleaners-table";
import {
    cleanersQueryParams,
    getCleanersList,
} from "@/src/services/cleaners.api";

const montserrat = Montserrat({
    weight: ["300", "400", "500", "600", "700"],
    subsets: ["latin"],
});

type Align = "ALL" | "NEW" | "ACTIVE" | "INACTIVE";

const Cleaners = () => {
    const cleanerDataList = [
        {
            id: "1",
            name: "<PERSON>",
            profile:
                "https://media-alacrity.s3.us-east-1.amazonaws.com/main_admin/fun-3d-cartoon-teenage-boy.jpg",
            email: "<EMAIL>",
            countryCode: "+1",
            phone: "555-1234",
            address: "Polarise Mall",
            createdAt: "2024-06-01",
            isTakenAction: false,
            status: null,
        },
        {
            id: "2",
            name: "Jane <PERSON>",
            email: "<EMAIL>",
            profile:
                "https://media-alacrity.s3.us-east-1.amazonaws.com/main_admin/fun-3d-cartoon-teenage-boy.jpg",
            countryCode: "+1",
            phone: "555-5678",
            address: "Polarise Mall",
            createdAt: "2024-05-15",
            isTakenAction: true,
            status: "ACTIVE",
        },
        {
            id: "3",
            name: "Alice Johnson",
            email: "<EMAIL>",
            profile:
                "https://media-alacrity.s3.us-east-1.amazonaws.com/main_admin/fun-3d-cartoon-teenage-boy.jpg",
            countryCode: "+44",
            phone: "7700-900123",
            address: "Polarise Mall",
            createdAt: "2024-04-20",
            isTakenAction: false,
            status: null,
        },
        {
            id: "4",
            name: "Bob Williams",
            email: "<EMAIL>",
            profile: null,
            countryCode: "+91",
            phone: "98765-43210",
            address: "Polarise Mall",
            createdAt: "2024-03-10",
            isTakenAction: true,
            status: "INACTIVE",
        },
    ];

    const [alignValue, setAlignValue] = useState<Align>("ALL");
    const [search, setSearch] = useState("");
    const [debouncedSearchQuery, setDebouncedSearchQuery] = useState(search);
    const [isDataFechingLoading, setIsDataFechingLoading] = useState(false);

    const [pagination, setPagination] = useState({
        current: 1,
        pageSize: 10,
        total: 0,
    });

    const handleSearch = useCallback(
        debounce((query: any) => {
            setDebouncedSearchQuery(query);
        }, 500),
        []
    );

    const fetchAllCleanersDataList = async (page = 1) => {
        try {
            setIsDataFechingLoading(true);
            const params: cleanersQueryParams = {
                orderBy: "createdAt|asc",
                skip: (page - 1) * pagination.pageSize,
                take: pagination.pageSize,
            };
            if (search) {
                params.search_column = ["name"];
                params.search = search;
            }
            const token = localStorage.getItem("token");
            const res = await getCleanersList(params, token);
            if (res.list.length !== 0) {
                const updatedData = res.list.map((item: any, index: any) => ({
                    ...item,
                    srNo: (page - 1) * pagination.pageSize + index + 1,
                }));
                // setCustomerDataList(updatedData);
                setPagination({
                    ...pagination,
                    current: page,
                    total: res.total,
                });
            } else {
                // setCustomerDataList(res.list);
                setPagination({
                    ...pagination,
                    current: page,
                    total: res.total,
                });
            }
        } catch (error) {
            console.error("Something wents wrong while fetching Customer.");
        } finally {
            setIsDataFechingLoading(false);
        }
    };

    return (
        <div className="w-full h-full p-5">
            <div className="flex justify-between items-center">
                <div className="custom-segmented flex items-center rounded-[15px] p-1 gap-2">
                    {CLEANERS_TABS.map((btn) => (
                        <button
                            key={btn.value}
                            type="button"
                            className={`custom-segmented-btn text-[14px] w-[100px] font-[600] px-4 py-2 rounded-[10px] transition-all duration-200 ${
                                alignValue === btn.value ? "selected" : ""
                            } ${montserrat.className}`}
                            onClick={() => setAlignValue(btn.value as Align)}
                        >
                            {btn.label}
                        </button>
                    ))}
                </div>
                <div className="flex items-center w-[280px] border border-gray-100 rounded-[10px] px-4 py-2 space-x-2 h-[40px] bg-white shadow-md">
                    <LuSearch className="w-[20px] h-[20px]" />
                    <input
                        type="text"
                        placeholder="Search"
                        className="flex-1 outline-none"
                        value={search}
                        onChange={(e) => {
                            setSearch(e.target.value);
                            handleSearch(e.target.value);
                        }}
                    />
                </div>
            </div>

            {/* Table Section */}
            {isDataFechingLoading ? (
                <div className="w-full h-[calc(100vh-230px)] flex justify-center items-center">
                    <Loader />
                </div>
            ) : (
                <div className="w-full mt-5 rounded-[10px] flex justify-center h-[calc(100%-58px)] overflow-y-auto scrollbar-hide sicky">
                    <ManageCleanerTable
                        data={cleanerDataList}
                        fetch={fetchAllCleanersDataList}
                        pagination={pagination}
                        fetchAllCleanersDataList={fetchAllCleanersDataList}
                        search={search}
                        alignValue={alignValue}
                    />
                </div>
            )}
        </div>
    );
};

export default Cleaners;
