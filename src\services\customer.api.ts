import { fetch } from "@/src/libs/helpers";

export interface customerQueryParams {
    take?: number;
    skip?: number;
    include?: string | string[];
    orderBy?: string;
    where?: any;
    search?: string;
    type?: string;
    grade?: string;
    level?: string;
    search_column?: string[];
    categoryId?: string;
    customerId?: string;
    status?: string;
}

export interface customerBody {
    category?: string;
    categoryId?: string;
    mainTitle?: string;
    subTitle?: string;
    image?: string;
    description?: string;
    customerId?: string;
    website?: string;
    status?: string;
    shortDescription?: string,
    newPosition?: number,
}

export const getCustomerList = async (
    params: customerQueryParams,
    authorization: string | null
): Promise<any> => {
    return fetch({
        url: `/customer`,
        method: "GET",
        params: params,
        headers: {
            Authorization: authorization,
        },
    });
};

